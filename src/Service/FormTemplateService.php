<?php

namespace App\Service;

use App\DTO\Form\FormTemplateCreateRequest;
use App\DTO\Form\FormTemplateUpdateRequest;
use App\Entity\App\Company;
use App\Entity\App\FormTemplate;
use App\Enum\Status;
use App\Repository\CompanyRepository;
use App\Repository\FormTemplateRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class FormTemplateService
{
    private EntityManagerInterface $entityManager;
    private FormTemplateRepository $formTemplateRepository;
    private CompanyRepository $companyRepository;
    private TenantManager $tenantManager;
    private TenantCacheService $tenantCache;
    private TenantLoggerService $logger;

    public function __construct(
        EntityManagerInterface $entityManager,
        FormTemplateRepository $formTemplateRepository,
        CompanyRepository $companyRepository,
        TenantManager $tenantManager,
        TenantCacheService $tenantCache,
        TenantLoggerService $logger
    ) {
        $this->entityManager = $entityManager;
        $this->formTemplateRepository = $formTemplateRepository;
        $this->companyRepository = $companyRepository;
        $this->tenantManager = $tenantManager;
        $this->tenantCache = $tenantCache;
        $this->logger = $logger;
    }

    /**
     * Obtiene todos los formularios activos del tenant actual
     */
    public function getActiveFormTemplates(): array
    {
        return $this->tenantCache->get('form_templates', function() {
            return $this->formTemplateRepository->findForCache();
        });
    }

    /**
     * Obtiene todos los formularios activos con empresas cargadas
     */
    public function getActiveFormTemplatesWithCompanies(): array
    {
        return $this->formTemplateRepository->findWithCompanies();
    }

    /**
     * Obtiene un formulario por ID
     */
    public function getFormTemplateById(int $id): FormTemplate
    {
        $formTemplate = $this->formTemplateRepository->find($id);

        if (!$formTemplate) {
            throw new NotFoundHttpException('Formulario no encontrado.');
        }

        return $formTemplate;
    }

    /**
     * Crea un nuevo formulario
     */
    public function createFormTemplate(FormTemplateCreateRequest $dto): FormTemplate
    {
        try {
            $this->entityManager->beginTransaction();

            $formTemplate = new FormTemplate();
            $formTemplate->setName($dto->getName());
            $formTemplate->setDescription($dto->getDescription());
            $formTemplate->setCreatedAt(new \DateTime());
            $formTemplate->setUpdatedAt(new \DateTime());
            $formTemplate->setStatus(Status::ACTIVE);

            // Manejar empresas asignadas
            $this->assignCompaniesToFormTemplate($formTemplate, $dto->getCompanyIds());

            $this->entityManager->persist($formTemplate);
            $this->entityManager->flush();

            // Limpiar caché
            $this->clearFormTemplatesCache();

            $this->logger->info('Form template created', [
                'id' => $formTemplate->getId(),
                'name' => $formTemplate->getName(),
                'companies' => $formTemplate->getCompanyNames()
            ]);

            $this->entityManager->commit();

            return $formTemplate;

        } catch (\Exception $e) {
            $this->entityManager->rollback();
            
            $this->logger->error('Error creating form template', [
                'error' => $e->getMessage(),
                'name' => $dto->getName()
            ]);

            throw $e;
        }
    }

    /**
     * Actualiza un formulario existente
     */
    public function updateFormTemplate(int $id, FormTemplateUpdateRequest $dto): FormTemplate
    {
        $formTemplate = $this->getFormTemplateById($id);

        try {
            $this->entityManager->beginTransaction();

            $formTemplate->setName($dto->getName());
            $formTemplate->setDescription($dto->getDescription());
            $formTemplate->setUpdatedAt(new \DateTime());

            // Actualizar empresas asignadas
            $this->updateFormTemplateCompanies($formTemplate, $dto->getCompanyIds());

            $this->entityManager->flush();

            // Limpiar caché
            $this->logger->info('About to clear cache after form template update', [
                'form_id' => $formTemplate->getId(),
                'form_name' => $formTemplate->getName()
            ]);
            $this->clearFormTemplatesCache();

            $this->logger->info('Form template updated', [
                'id' => $formTemplate->getId(),
                'name' => $formTemplate->getName(),
                'companies' => $formTemplate->getCompanyNames()
            ]);

            $this->entityManager->commit();

            return $formTemplate;

        } catch (\Exception $e) {
            $this->entityManager->rollback();
            
            $this->logger->error('Error updating form template', [
                'error' => $e->getMessage(),
                'form_id' => $id
            ]);

            throw $e;
        }
    }

    /**
     * Elimina un formulario (soft delete)
     */
    public function deleteFormTemplate(int $id): void
    {
        $formTemplate = $this->getFormTemplateById($id);

        try {
            $this->entityManager->beginTransaction();

            $formTemplate->setStatus(Status::INACTIVE);
            $formTemplate->setUpdatedAt(new \DateTime());

            $this->entityManager->flush();

            // Limpiar caché
            $this->clearFormTemplatesCache();

            $this->logger->info('Form template deleted', [
                'id' => $formTemplate->getId(),
                'name' => $formTemplate->getName()
            ]);

            $this->entityManager->commit();

        } catch (\Exception $e) {
            $this->entityManager->rollback();
            
            $this->logger->error('Error deleting form template', [
                'error' => $e->getMessage(),
                'form_id' => $id
            ]);

            throw $e;
        }
    }

    /**
     * Busca formularios por nombre
     */
    public function searchFormTemplates(string $searchTerm): array
    {
        return $this->formTemplateRepository->searchByName($searchTerm);
    }

    /**
     * Obtiene estadísticas de formularios
     */
    public function getFormTemplateStats(): array
    {
        return [
            'total_forms' => $this->formTemplateRepository->countActive(),
            'forms_with_fields' => count($this->formTemplateRepository->findWithFields())
        ];
    }

    /**
     * Valida acceso al formulario (ya no necesario con multi-tenant por BD)
     */
    public function validateFormTemplateAccess(FormTemplate $formTemplate): void
    {
        // Ya no es necesario validar tenant - el aislamiento es por base de datos
        // Si el formulario existe en esta BD, el usuario tiene acceso
    }

    /**
     * Duplica un formulario existente
     */
    public function duplicateFormTemplate(int $id, string $newName): FormTemplate
    {
        $originalTemplate = $this->getFormTemplateById($id);

        try {
            $this->entityManager->beginTransaction();

            $newTemplate = new FormTemplate();
            $newTemplate->setName($newName);
            $newTemplate->setDescription($originalTemplate->getDescription() . ' (Copia)');
            $newTemplate->setCreatedAt(new \DateTime());
            $newTemplate->setUpdatedAt(new \DateTime());
            $newTemplate->setStatus(Status::ACTIVE);

            $this->entityManager->persist($newTemplate);
            $this->entityManager->flush();

            // Limpiar caché
            $this->clearFormTemplatesCache();

            $this->logger->info('Form template duplicated', [
                'original_id' => $originalTemplate->getId(),
                'new_id' => $newTemplate->getId(),
                'new_name' => $newName
            ]);

            $this->entityManager->commit();

            return $newTemplate;

        } catch (\Exception $e) {
            $this->entityManager->rollback();
            
            $this->logger->error('Error duplicating form template', [
                'error' => $e->getMessage(),
                'original_id' => $id
            ]);

            throw $e;
        }
    }

    /**
     * Asigna empresas a un formulario durante la creación
     */
    private function assignCompaniesToFormTemplate(FormTemplate $formTemplate, array $companyIds): void
    {
        if (empty($companyIds)) {
            // Array vacío significa que el formulario está disponible para todas las empresas
            return;
        }

        foreach ($companyIds as $companyId) {
            $company = $this->companyRepository->find($companyId);
            if ($company && $company->getStatus() === Status::ACTIVE) {
                $formTemplate->addCompany($company);
            }
        }
    }

    /**
     * Actualiza las empresas asignadas a un formulario
     */
    private function updateFormTemplateCompanies(FormTemplate $formTemplate, array $companyIds): void
    {
        // Remover todas las empresas actuales
        foreach ($formTemplate->getCompanies() as $company) {
            $formTemplate->removeCompany($company);
        }

        // Asignar las nuevas empresas
        $this->assignCompaniesToFormTemplate($formTemplate, $companyIds);
    }

    /**
     * Obtiene formularios disponibles para una empresa específica
     */
    public function getFormTemplatesForCompany(Company $company): array
    {
        return $this->formTemplateRepository->findAvailableForCompany($company);
    }

    /**
     * Verifica si un formulario está disponible para una empresa
     */
    public function isFormTemplateAvailableForCompany(int $formTemplateId, Company $company): bool
    {
        return $this->formTemplateRepository->isAvailableForCompany($formTemplateId, $company);
    }

    /**
     * Obtiene todas las empresas activas del sistema
     */
    public function getActiveCompanies(): array
    {
        return $this->companyRepository->findBy(['status' => Status::ACTIVE], ['name' => 'ASC']);
    }

    /**
     * Limpia el caché de formularios
     */
    private function clearFormTemplatesCache(): void
    {
        $this->tenantCache->delete('form_templates');

        // También limpiar los caches del API que están organizados por empresa
        // Obtener todas las empresas activas para limpiar sus caches específicos
        $companies = $this->getActiveCompanies();

        foreach ($companies as $company) {
            $this->tenantCache->delete('forms_index_company_' . $company->getId());
        }

        // También limpiar el cache para usuarios sin empresa
        $this->tenantCache->delete('forms_index_company_all');

        $this->logger->info('Form templates cache cleared', [
            'tenant' => $this->tenantManager->getCurrentTenant(),
            'companies_cleared' => count($companies)
        ]);
    }
}
