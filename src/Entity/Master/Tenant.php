<?php

namespace App\Entity\Master;

use App\Enum\Status;
use App\Repository\TenantRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: TenantRepository::class)]
class Tenant
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 1, enumType: Status::class)]
    private ?Status $beneficios = null;

    #[ORM\Column(type: 'string', length: 1, enumType: Status::class)]
    private ?Status $formularios = null;

    #[ORM\Column(type: 'string', length: 1, enumType: Status::class)]
    private ?Status $eventos = null;

    #[ORM\Column(type: 'string', length: 1, enumType: Status::class)]
    private ?Status $checador = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $aviso = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $logo = null;

    #[ORM\Column(length: 50)]
    private ?string $dominio = null;

    #[ORM\Column(type: 'string', length: 2, enumType: Status::class)]
    private ?Status $status = null;

    #[ORM\Column(length: 255)]
    private ?string $databaseName = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getBeneficios(): ?Status
    {
        return $this->beneficios;
    }

    public function setBeneficios(Status $beneficios): static
    {
        $this->beneficios = $beneficios;

        return $this;
    }

    public function getFormularios(): ?Status
    {
        return $this->formularios;
    }

    public function setFormularios(Status $formularios): static
    {
        $this->formularios = $formularios;

        return $this;
    }

    public function getEventos(): ?Status
    {
        return $this->eventos;
    }

    public function setEventos(Status $eventos): static
    {
        $this->eventos = $eventos;

        return $this;
    }

    public function getChecador(): ?Status
    {
        return $this->checador;
    }

    public function setChecador(Status $checador): static
    {
        $this->checador = $checador;

        return $this;
    }

    public function getAviso(): ?string
    {
        return $this->aviso;
    }

    public function setAviso(string $aviso): static
    {
        $this->aviso = $aviso;

        return $this;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function setLogo(string $logo): static
    {
        $this->logo = $logo;

        return $this;
    }

    public function getDominio(): ?string
    {
        return $this->dominio;
    }

    public function setDominio(string $dominio): static
    {
        $this->dominio = $dominio;

        return $this;
    }

    public function getStatus(): ?Status
    {
        return $this->status;
    }

    public function setStatus(Status $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getDatabaseName(): ?string
    {
        return $this->databaseName;
    }

    public function setDatabaseName(string $databaseName): static
    {
        $this->databaseName = $databaseName;

        return $this;
    }
}
