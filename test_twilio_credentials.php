<?php

require_once 'vendor/autoload.php';

use Twilio\Rest\Client;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$accountSid = $_ENV['TWILIO_ACCOUNT_SID'] ?? null;
$authToken = $_ENV['TWILIO_AUTH_TOKEN'] ?? null;

echo "Testing Twilio Credentials...\n";
echo "Account SID: " . ($accountSid ? substr($accountSid, 0, 10) . "..." : "NOT SET") . "\n";
echo "Auth Token: " . ($authToken ? substr($authToken, 0, 4) . "..." . substr($authToken, -4) : "NOT SET") . "\n\n";

if (!$accountSid || !$authToken) {
    echo "❌ ERROR: Twilio credentials not found in environment variables\n";
    exit(1);
}

try {
    $client = new Client($accountSid, $authToken);
    
    echo "Attempting to fetch account information...\n";
    $account = $client->account->fetch();
    
    echo "✅ SUCCESS: Twilio credentials are valid!\n";
    echo "Account Status: " . $account->status . "\n";
    echo "Account Type: " . $account->type . "\n";
    echo "Date Created: " . $account->dateCreated->format('Y-m-d H:i:s') . "\n";
    
} catch (\Twilio\Exceptions\RestException $e) {
    echo "❌ TWILIO ERROR: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n";
    echo "HTTP Status: " . $e->getStatusCode() . "\n";
    
    if ($e->getStatusCode() === 401) {
        echo "\n🔍 DIAGNOSIS: Invalid credentials (Account SID or Auth Token)\n";
        echo "Please check your Twilio console for the correct credentials.\n";
    }
    
    exit(1);
} catch (\Exception $e) {
    echo "❌ GENERAL ERROR: " . $e->getMessage() . "\n";
    exit(1);
}
