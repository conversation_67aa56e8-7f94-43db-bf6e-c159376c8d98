{% extends 'base-master.html.twig' %}

{% block title %}Aplicaciones{% endblock %}

{% block body %}
    <div class="section-title">
        <h1 class="glass" style="color: white">Aplicaciones </h1>
    </div>
    <div class="section-table">

        <div class="container-add-button">
            <a href="{{ path('app_master_new', {'dominio': dominio}) }}" class="glass add-button">Agregar</a>
        </div>

        <table>
            <thead>
            <tr>
                <th/>
                <th>Dominio</th>
                <th>Base</th>
                <th>Beneficios</th>
                <th>Formularios</th>
                <th>Eventos</th>
                <th>Checador</th>
                <th>Aviso</th>
                <th>Logo</th>
                <th></th>
            </tr>
            </thead>
            <tbody>

            {% for tenant in tenants %}
            <tr>
                <td>
                    <div class="status-cell">
                        <div class="circle {{ tenant.status.value == '1' ? 'circle-activated' : 'circle-deactivated' }}"></div>
                    </div>
                </td>

                <td>{{ tenant.dominio }}</td>
                <td>{{ tenant.databaseName }}</td>
                <td>
                    {% if tenant.beneficios.value == '1'%}
                        Activado
                    {% else %}
                        Desactivado
                    {% endif %}
                </td>
                <td>
                    {% if tenant.formularios.value == '1'%}
                        Activado
                    {% else %}
                        Desactivado
                    {% endif %}
                </td>
                <td>
                    {% if tenant.eventos.value == '1' %}
                        Activado
                    {% else %}
                        Desactivado
                    {% endif %}
                </td>
                <td>
                    {% if tenant.checador.value == '1'%}
                        Activado
                    {% else %}
                        Desactivado
                    {% endif %}
                </td>
                <td>
                    {%if tenant.aviso == '' %}
                        Vacío
                    {% else %}
                        Cargado
                    {%  endif %}
                </td>
                <td>
                    {% if tenant.logo == '' %}
                        Vacío
                    {% else %}
                        Cargado
                    {% endif %}
                </td>
                <td>
                    <a href="{{ path('app_master_edit', {'dominio': dominio, 'rtenant': tenant.id}) }}" style="text-decoration: none;" class="action-button btn-editar">Editar</a>
                    {% if tenant.dominio != dominio %}
                        <form
                                method="post"
                                action="{{ path('app_master_delete', {'dominio': dominio, 'rtenant': tenant.id}) }}"
                                class="form-delete"
                                onsubmit="return confirm('¿Estás seguro de que deseas eliminar este tenant?');"
                                data-dominio="{{ tenant.dominio }}"
                        >
                            <input
                                    type="hidden"
                                    name="_token"
                                    value="{{ csrf_token('delete' ~ tenant.id) }}"
                            >
                            <button
                                    style="width: 100%;
                                    border-width: 0 "
                                    class="action-button {{
                                    tenant.status.value == '0'
                                    ? 'btn-activar'
                                    : 'btn-desactivar' }}"
                                    type="submit">{{
                                tenant.status.value == '0'
                                ? 'Activar'
                                : 'Desactivar' }}
                            </button>
                        </form>
                    {% endif %}
                </td>
            </tr>
            {% else %}
            <tr>
                <td colspan="8" >No hay tenants</td>
                <td style="display: none"></td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}

